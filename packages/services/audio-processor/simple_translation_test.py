#!/usr/bin/env python3
"""
Simple translation test script that directly tests the qwen-service API
"""

import asyncio
import httpx
import json


async def test_qwen_service_directly():
    """Test qwen-service directly to see if it's working"""
    print("Testing qwen-service directly...")
    
    # Test the backend service endpoint that audio-processor uses
    backend_url = "http://localhost:8000/api/v1/qwen/generate"
    
    # Create a test translation request similar to what audio-processor sends
    test_payload = {
        "prompt": "ترجم النص التالي من العربية إلى 中文 مع الحفاظ على المعنى والسياق:\n\nالسياق: مرحبا بكم في هذا البرنامج التعليمي سنتعلم اليوم عن الذكاء الاصطناعي وكيفية استخدامه في التطبيقات المختلفة\n\nالنص المطلوب ترجمته: سنتعلم اليوم عن الذكاء الاصطناعي\n\nالترجمة:",
        "system_prompt": "أنت مترجم محترف متخصص في ترجمة النصوص العربية إلى الصينية. احرص على الحفاظ على المعنى الأصلي والسياق والنبرة. قدم الترجمة إلى الصينية فقط دون تفسيرات إضافية.",
        "enable_thinking": True,
        "source_service": "audio-processor"
    }
    
    try:
        async with httpx.AsyncClient(timeout=60.0) as client:
            print(f"Sending request to: {backend_url}")
            print(f"Payload: {json.dumps(test_payload, ensure_ascii=False, indent=2)}")
            
            response = await client.post(backend_url, json=test_payload)
            print(f"Response status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"Response: {json.dumps(result, ensure_ascii=False, indent=2)}")
                
                # Check if we got a translation
                generated_text = result.get("generated_text", "")
                if generated_text:
                    print(f"\n✅ Translation successful!")
                    print(f"Generated text: {generated_text}")
                else:
                    print(f"\n❌ No generated text in response")
            else:
                print(f"❌ Request failed with status {response.status_code}")
                print(f"Response: {response.text}")
                
    except Exception as e:
        print(f"❌ Error testing qwen-service: {e}")


async def test_qwen_service_health():
    """Test if qwen-service is running"""
    print("Testing qwen-service health...")
    
    health_url = "http://localhost:8000/api/v1/qwen/health"
    
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.get(health_url)
            print(f"Health check status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"Health response: {json.dumps(result, ensure_ascii=False, indent=2)}")
                return True
            else:
                print(f"Health check failed: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False


async def main():
    """Main test function"""
    print("=== Translation Functionality Investigation ===\n")
    
    # First check if services are running
    health_ok = await test_qwen_service_health()
    
    if health_ok:
        print("\n" + "="*50)
        await test_qwen_service_directly()
    else:
        print("\n❌ Cannot proceed - qwen-service is not healthy")


if __name__ == "__main__":
    asyncio.run(main())
