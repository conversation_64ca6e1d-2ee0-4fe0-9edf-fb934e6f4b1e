"""
Translation service implementation

Provides context-aware translation of transcription segments using
the Qwen model via backend service integration.
"""

from loguru import logger

from core.config import Settings
from interfaces.qwen_service_client import QwenServiceClient
from interfaces.translation_service import TranslationService
from models.domain.transcription_segment import TranscriptionSegment


class TranslationServiceImpl(TranslationService):
    """Implementation of translation service with context awareness"""

    def __init__(self, settings: Settings, qwen_client: QwenServiceClient):
        """
        Initialize translation service

        Args:
            settings: Application settings containing translation configuration
            qwen_client: Client for communicating with backend Qwen service
        """
        self.settings = settings
        self.qwen_client = qwen_client

    async def translate_segments(
        self, segments: list[TranscriptionSegment], target_language: str = "zh"
    ) -> list[TranscriptionSegment]:
        """
        Translate transcription segments with context awareness

        For each segment, creates context by including 4 previous and 4 next segments,
        then sends the contextualized text to the translation model.

        Args:
            segments: List of transcription segments to translate
            target_language: Target language code for translation (default: "zh")

        Returns:
            List of transcription segments with translated_text field populated

        Raises:
            RuntimeError: If translation fails
        """
        if not segments:
            logger.info("No segments to translate")
            return segments

        logger.info(f"Starting translation of {len(segments)} segments")
        translated_segments = []

        for i, segment in enumerate(segments):
            try:
                # Build context with 4 previous and 4 next segments
                context_text = self._build_context(segments, i)

                # Create translation prompt with target language
                translation_prompt = self._create_translation_prompt(
                    segment.text, context_text, target_language
                )

                # Create dynamic system prompt for target language
                system_prompt = self._create_system_prompt(target_language)

                # Call translation service with dynamic system prompt
                translation_result = await self.qwen_client.translate_text(
                    prompt=translation_prompt,
                    system_prompt=system_prompt,
                )

                if translation_result.success:
                    # Create new segment with translation
                    translated_segment = TranscriptionSegment(
                        start=segment.start,
                        end=segment.end,
                        text=segment.text,
                        speaker=segment.speaker,
                        words=segment.words,
                        translated_text=translation_result.generated_text.strip(),
                    )
                    translated_segments.append(translated_segment)
                    logger.debug(
                        f"Successfully translated segment {i + 1}/{len(segments)}"
                    )
                else:
                    # Keep original segment without translation on failure
                    logger.warning(
                        f"Translation failed for segment {i + 1}: "
                        f"{translation_result.message}"
                    )
                    translated_segments.append(segment)

            except Exception as e:
                logger.error(f"Error translating segment {i + 1}: {e}")
                # Keep original segment without translation on error
                translated_segments.append(segment)

        logger.info(f"Translation completed for {len(translated_segments)} segments")
        return translated_segments

    def _build_context(
        self, segments: list[TranscriptionSegment], current_index: int
    ) -> str:
        """
        Build context text using same approach as chapter detection

        Args:
            segments: All transcription segments
            current_index: Index of current segment being translated

        Returns:
            Context text containing surrounding segments
        """
        # Use same context window as chapter detection for consistency
        context_segments = self.settings.TRANSLATION_CONTEXT_SEGMENTS
        start_idx = max(0, current_index - context_segments)
        end_idx = min(len(segments), current_index + context_segments + 1)

        # Simple text concatenation like chapter detection
        return " ".join(segments[i].text for i in range(start_idx, end_idx))

    def _create_translation_prompt(
        self, target_text: str, context_text: str, target_language: str
    ) -> str:
        """
        Create translation prompt with dynamic target language

        Args:
            target_text: The specific text to translate
            context_text: Surrounding context for better translation
            target_language: Target language code for translation

        Returns:
            Formatted prompt for translation
        """
        # Get target language name from settings configuration
        target_lang_name = self.settings.TRANSLATION_LANGUAGE_NAMES.get(
            target_language, target_language
        )

        # Use dynamic prompt template from settings
        prompt_template = self.settings.TRANSLATION_PROMPT_TEMPLATE.format(
            target_language=target_lang_name
        )

        return prompt_template.format(target_text=target_text, context=context_text)

    def _create_system_prompt(self, target_language: str) -> str:
        """
        Create dynamic system prompt based on target language

        Args:
            target_language: Target language code for translation

        Returns:
            System prompt for the target language
        """
        # Get target language name in Arabic from settings configuration
        target_lang_name = self.settings.TRANSLATION_LANGUAGE_NAMES_ARABIC.get(
            target_language, target_language
        )

        # Use dynamic system prompt template from settings
        return self.settings.TRANSLATION_SYSTEM_PROMPT.format(
            target_language=target_lang_name
        )
