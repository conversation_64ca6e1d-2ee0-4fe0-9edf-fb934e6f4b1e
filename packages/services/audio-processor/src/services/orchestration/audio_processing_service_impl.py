"""
Main audio processing orchestration service

Coordinates the complete audio processing workflow including transcription,
chapter detection, and chapter splitting with proper error handling and reporting.
"""

from typing import Optional

from loguru import logger

from core.config import Settings
from interfaces import (
    AudioTranscriptionService,
    ChapterDetectionService,
    ChapterSplittingService,
    TranslationService,
)
from models.api.process_report_entry import ProcessReportEntry
from models.domain.chapter import Chapter
from models.services.audio_processing_request import AudioProcessingRequest
from models.services.audio_processing_response import AudioProcessingResponse


class AudioProcessingServiceImpl:
    """
    Implementation of main audio processing orchestration service

    Coordinates the complete workflow: transcription -> chapter detection -> chapter splitting
    with proper error handling, logging, and resource management.
    """

    def __init__(
        self,
        settings: Settings,
        transcription_service: AudioTranscriptionService,
        chapter_detection_service: ChapterDetectionService,
        chapter_splitting_service: ChapterSplittingService,
        translation_service: Optional[TranslationService] = None,
    ):
        """
        Initialize the audio processing service

        Args:
            settings: Application settings
            transcription_service: Service for audio transcription
            chapter_detection_service: Service for chapter detection
            chapter_splitting_service: Service for chapter splitting
            translation_service: Optional service for text translation
        """
        self.settings = settings
        self.transcription_service = transcription_service
        self.chapter_detection_service = chapter_detection_service
        self.chapter_splitting_service = chapter_splitting_service
        self.translation_service = translation_service
        self._initialized = False

    async def process_audio(
        self, request: AudioProcessingRequest
    ) -> AudioProcessingResponse:
        """
        Process an audio file through complete workflow: transcription, chapter detection, and splitting

        Args:
            request: Audio processing request containing file path and configuration

        Returns:
            AudioProcessingResponse containing all processing results

        Raises:
            RuntimeError: If processing fails
        """
        if not self._initialized:
            await self.initialize()

        try:
            logger.info(f"Starting audio processing for: {request.audio_file_path}")

            # Step 1: Transcription
            logger.info("Step 1: Audio transcription")
            transcription_result = await self.transcription_service.transcribe_audio(
                request.config
            )

            if not transcription_result.success:
                return AudioProcessingResponse(
                    success=False,
                    message=f"Transcription failed: {transcription_result.message}",
                    segments=[],
                    processing_time=transcription_result.processing_time,
                )

            logger.success(
                f"Transcription completed: {len(transcription_result.segments)} segments"
            )

            # Step 1.5: Translation (if requested)
            segments_to_process = transcription_result.segments
            if request.config.enable_translation and self.translation_service:
                logger.info("Step 1.5: Text translation")
                try:
                    translated_segments = (
                        await self.translation_service.translate_segments(
                            transcription_result.segments,
                            target_language=request.config.translation_target_language
                            or "zh",
                        )
                    )
                    segments_to_process = translated_segments
                    logger.success(
                        f"Translation completed for {len(translated_segments)} segments"
                    )
                except Exception as e:
                    logger.warning(
                        f"Translation failed, continuing without translation: {e}"
                    )
                    # Continue with original segments if translation fails

            # Initialize response with transcription results
            response = AudioProcessingResponse(
                success=True,
                message="Audio processing completed",
                segments=segments_to_process,
                language=transcription_result.language,
                duration=transcription_result.duration,
                processing_time=transcription_result.processing_time,
                word_count=transcription_result.word_count,
            )

            # Step 2: Chapter Detection (if requested)
            chapters_list: Optional[list[Chapter]] = None
            detection_report: Optional[list[ProcessReportEntry]] = None

            if self._should_detect_chapters(request.config):
                logger.info("Step 2: Chapter detection")

                if request.config.chapter_timestamps:
                    # Mode 2: Timestamp-based chapter detection
                    logger.info("Using timestamp-based chapter detection")
                    detection_result = (
                        self.chapter_detection_service.detect_chapters_by_timestamps(
                            segments=transcription_result.segments,
                            timestamps=request.config.chapter_timestamps,
                        )
                    )
                elif request.config.chapter_markers:
                    # Mode 3: Marker-based chapter detection
                    logger.info("Using marker-based chapter detection")
                    detection_result = (
                        await self.chapter_detection_service.detect_chapters_by_markers(
                            segments=transcription_result.segments,
                            chapter_markers=request.config.chapter_markers,
                        )
                    )
                else:
                    detection_result = None

                if detection_result and detection_result.success:
                    logger.success(
                        f"Chapter detection completed: {len(detection_result.chapters)} chapters"
                    )

                    # Update segments if reorganized
                    if detection_result.reorganized_segments:
                        response.segments = detection_result.reorganized_segments

                    detection_report = detection_result.detection_report

                    # Step 3: Chapter Splitting (if requested)
                    if request.config.split_chapters and detection_result.chapters:
                        logger.info("Step 3: Chapter splitting")

                        split_result = self.chapter_splitting_service.split_chapters(
                            audio_file_path=request.audio_file_path,
                            segments=response.segments,
                            raw_chapters=detection_result.chapters,
                            audio_duration=response.duration or 0.0,
                        )

                        if split_result.success:
                            chapters_list = split_result.chapters
                            logger.success(
                                f"Chapter splitting completed: {len(chapters_list)} chapters"
                            )

                            # Merge split report with detection report
                            if detection_report is None:
                                detection_report = []
                            if split_result.split_report:
                                detection_report.extend(split_result.split_report)
                        else:
                            logger.warning(
                                f"Chapter splitting failed: {split_result.message}"
                            )
                            # Continue with detection results even if splitting fails
                            chapters_list = self._calculate_chapter_end_times(
                                detection_result.chapters, response.duration or 0.0
                            )
                    else:
                        # Just calculate end times for chapter boundaries
                        chapters_list = self._calculate_chapter_end_times(
                            detection_result.chapters, response.duration or 0.0
                        )
                else:
                    logger.warning("Chapter detection failed or returned no results")

            # Update response with chapter information
            response.chapters = chapters_list
            response.report = detection_report

            # Update success message
            message_parts = ["Audio processing completed"]
            if chapters_list:
                message_parts.append(f"with {len(chapters_list)} chapters")
                if request.config.split_chapters:
                    message_parts.append("and chapter splitting")

            response.message = " ".join(message_parts)

            logger.success(response.message)
            return response

        except Exception as e:
            error_msg = f"Audio processing failed: {e}"
            logger.error(error_msg)
            return AudioProcessingResponse(
                success=False,
                message=error_msg,
                segments=[],
            )

    def _should_detect_chapters(self, config) -> bool:
        """
        Check if chapter detection should be performed

        Args:
            config: Transcription configuration

        Returns:
            True if chapter detection is requested, False otherwise
        """
        return bool(config.chapter_timestamps or config.chapter_markers)

    def _calculate_chapter_end_times(
        self, chapters: list[dict], audio_duration: float
    ) -> list[Chapter]:
        """
        Calculate end times for chapters based on next chapter's start time

        Args:
            chapters: List of detected chapters
            audio_duration: Total audio duration in seconds

        Returns:
            List of Chapter objects with start and end times
        """
        result_chapters = []

        for i, chapter in enumerate(chapters):
            # Set end time to next chapter's start time or audio end
            if i < len(chapters) - 1:
                end_time = chapters[i + 1]["start_time"]
            else:
                end_time = audio_duration

            result_chapters.append(
                Chapter(
                    title=chapter["title"],
                    start_time=chapter["start_time"],
                    end_time=end_time,
                )
            )

        return result_chapters

    async def initialize(self) -> None:
        """
        Initialize all underlying services

        Raises:
            RuntimeError: If initialization fails
        """
        if self._initialized:
            return

        try:
            logger.info("Initializing audio processing orchestration service...")

            # Initialize transcription service
            await self.transcription_service.initialize()

            # Chapter detection and splitting services don't need async initialization

            self._initialized = True
            logger.success(
                "Audio processing orchestration service initialized successfully"
            )

        except Exception as e:
            logger.error(f"Failed to initialize audio processing service: {e}")
            raise RuntimeError("Audio processing service initialization failed") from e

    async def cleanup(self) -> None:
        """
        Cleanup all underlying services and resources
        """
        if not self._initialized:
            return

        try:
            logger.info("Cleaning up audio processing orchestration service...")

            # Cleanup transcription service
            await self.transcription_service.cleanup()

            # Chapter detection and splitting services don't need async cleanup

            self._initialized = False
            logger.success("Audio processing orchestration service cleanup completed")

        except Exception as e:
            logger.error(f"Error during audio processing service cleanup: {e}")
            # Continue with cleanup even if some operations fail
