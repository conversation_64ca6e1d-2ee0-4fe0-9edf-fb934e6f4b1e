"""
Transcription configuration models

Contains configuration classes for transcription operations,
including parameters and validation logic.
"""

from dataclasses import dataclass
from typing import Optional


@dataclass
class TranscriptionConfig:
    """
    Transcription configuration data class

    Uses dataclass to encapsulate transcription parameters, providing better
    type checking and default value handling. This class serves as a parameter
    container for the TranscriptionService.transcribe() method.
    """

    audio_file: str
    language: Optional[str] = "ar"
    vad_onset: float = 0.5
    vad_offset: float = 0.363
    chunk_size: int = 30
    enable_diarization: bool = False
    min_speakers: Optional[int] = None
    max_speakers: Optional[int] = None
    # Chapter detection parameters
    chapter_timestamps: Optional[list[float]] = None
    chapter_markers: Optional[list[str]] = None
    split_chapters: bool = False
    # Translation parameters
    enable_translation: bool = False
    translation_target_language: Optional[str] = "zh"  # Default to Chinese

    def __post_init__(self) -> None:
        """Data validation"""
        if self.vad_onset < 0 or self.vad_onset > 1:
            msg = "vad_onset must be between 0-1"
            raise ValueError(msg)
        if self.vad_offset < 0 or self.vad_offset > 1:
            msg = "vad_offset must be between 0-1"
            raise ValueError(msg)
        if self.chunk_size <= 0:
            msg = "chunk_size must be greater than 0"
            raise ValueError(msg)
        if self.min_speakers is not None and self.min_speakers < 1:
            msg = "min_speakers must be greater than or equal to 1"
            raise ValueError(msg)
        if self.max_speakers is not None and self.max_speakers < 1:
            msg = "max_speakers must be greater than or equal to 1"
            raise ValueError(msg)
        if (
            self.min_speakers is not None
            and self.max_speakers is not None
            and self.min_speakers > self.max_speakers
        ):
            msg = "min_speakers cannot be greater than max_speakers"
            raise ValueError(msg)
