#!/usr/bin/env python3
"""
Simple test script for translation functionality

Tests the translation service integration with the audio processor.
"""

import asyncio
import sys
from pathlib import Path


# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from core.config import Settings
from infrastructure.qwen_service_client_impl import QwenServiceClientImpl
from models.domain.transcription_segment import TranscriptionSegment
from services.translation.translation_service_impl import TranslationServiceImpl


async def test_translation():
    """Test translation functionality"""
    print("Testing translation functionality...")

    # Create settings
    settings = Settings()

    # Create qwen client
    qwen_client = QwenServiceClientImpl(settings)

    # Create translation service
    translation_service = TranslationServiceImpl(settings, qwen_client)

    # Create test segments
    test_segments = [
        TranscriptionSegment(
            start=0.0, end=5.0, text="مرحبا بكم في هذا البرنامج التعليمي"
        ),
        TranscriptionSegment(
            start=5.0, end=10.0, text="سنتعلم اليوم عن الذكاء الاصطناعي"
        ),
        TranscriptionSegment(
            start=10.0, end=15.0, text="وكيفية استخدامه في التطبيقات المختلفة"
        ),
    ]

    print(f"Original segments: {len(test_segments)}")
    for i, segment in enumerate(test_segments):
        print(f"  {i + 1}: {segment.text}")

    try:
        # Test translation with Chinese as target language
        translated_segments = await translation_service.translate_segments(
            test_segments, target_language="zh"
        )

        print(f"\nTranslated segments: {len(translated_segments)}")
        for i, segment in enumerate(translated_segments):
            print(f"  {i + 1}: {segment.text}")
            if segment.translated_text:
                print(f"      -> {segment.translated_text}")
            else:
                print("      -> (no translation)")

        print("\nTranslation test completed successfully!")

    except Exception as e:
        print(f"Translation test failed: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_translation())
