#!/usr/bin/env python3
"""
Direct qwen-service test to bypass backend service
"""

import asyncio
import httpx
import json


async def test_direct_qwen_service():
    """Test qwen-service directly on port 8004"""
    print("Testing qwen-service directly on port 8004...")
    
    # Test the direct qwen-service endpoint
    qwen_url = "http://localhost:8004/api/v1/generate"
    
    # Create a test translation request
    test_payload = {
        "prompt": "ترجم النص التالي من العربية إلى الصينية مع الحفاظ على المعنى والسياق:\n\nالسياق: مرحبا بكم في هذا البرنامج التعليمي سنتعلم اليوم عن الذكاء الاصطناعي وكيفية استخدامه في التطبيقات المختلفة\n\nالنص المطلوب ترجمته: سنتعلم اليوم عن الذكاء الاصطناعي\n\nالترجمة:",
        "system_prompt": "أنت مترجم محترف متخصص في ترجمة النصوص العربية إلى الصينية. احرص على الحفاظ على المعنى الأصلي والسياق والنبرة. قدم الترجمة إلى الصينية فقط دون تفسيرات إضافية.",
        "enable_thinking": True
    }
    
    try:
        async with httpx.AsyncClient(timeout=60.0) as client:
            print(f"Sending request to: {qwen_url}")
            print(f"Payload: {json.dumps(test_payload, ensure_ascii=False, indent=2)}")
            
            response = await client.post(qwen_url, json=test_payload)
            print(f"Response status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"Response keys: {list(result.keys())}")
                
                # Check if we got a translation
                generated_text = result.get("generated_text", "")
                if generated_text:
                    print(f"\n✅ Translation successful!")
                    print(f"Generated text: {generated_text}")
                    print(f"Processing time: {result.get('processing_time', 'N/A')} seconds")
                    print(f"Thinking mode: {result.get('thinking_mode', 'N/A')}")
                else:
                    print(f"\n❌ No generated text in response")
                    print(f"Full response: {json.dumps(result, ensure_ascii=False, indent=2)}")
            else:
                print(f"❌ Request failed with status {response.status_code}")
                print(f"Response: {response.text}")
                
    except Exception as e:
        print(f"❌ Error testing qwen-service: {e}")
        import traceback
        traceback.print_exc()


async def test_simple_generation():
    """Test simple text generation"""
    print("\nTesting simple text generation...")
    
    qwen_url = "http://localhost:8004/api/v1/generate"
    
    simple_payload = {
        "prompt": "مرحبا، كيف حالك؟",
        "system_prompt": "أنت مساعد ذكي يتحدث العربية.",
        "enable_thinking": False
    }
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(qwen_url, json=simple_payload)
            print(f"Simple generation status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                generated_text = result.get("generated_text", "")
                print(f"Simple generation result: {generated_text}")
            else:
                print(f"Simple generation failed: {response.text}")
                
    except Exception as e:
        print(f"❌ Simple generation error: {e}")


async def main():
    """Main test function"""
    print("=== Direct Qwen Service Test ===\n")
    
    await test_simple_generation()
    print("\n" + "="*50)
    await test_direct_qwen_service()


if __name__ == "__main__":
    asyncio.run(main())
