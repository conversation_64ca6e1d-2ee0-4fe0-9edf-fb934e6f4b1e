"""
Backend service factory implementation

Creates and manages backend services with proper dependency injection,
following the clean architecture pattern and ensuring proper lifecycle management.
"""

from typing import Any, Optional

from loguru import logger

from app.core.settings import Settings
from app.services.qwen.infrastructure.qwen_service_factory import QwenServiceFactory
from app.services.qwen.interfaces import QwenServiceInterface


class BackendServiceFactory:
    """
    Factory for creating backend services with proper dependency injection

    Manages the lifecycle of all service instances and ensures proper
    configuration and dependency injection throughout the application.
    """

    def __init__(self, settings: Settings):
        """
        Initialize the service factory

        Args:
            settings: Application settings containing configuration
        """
        self.settings = settings
        self._initialized = False

        # Service factories for different services
        self._service_factories: dict[str, Any] = {}

        # Service instances (lazy initialization)
        self._qwen_service: Optional[QwenServiceInterface] = None

    def get_service_factory(self, service_name: str) -> Any:
        """
        Get service-specific factory

        Args:
            service_name: Name of the service (e.g., "qwen", "audio", etc.)

        Returns:
            Service-specific factory instance

        Raises:
            ValueError: If service is not supported
        """
        if service_name not in self._service_factories:
            if service_name == "qwen":
                self._service_factories["qwen"] = QwenServiceFactory(self.settings)
            else:
                raise ValueError(f"Unknown service: {service_name}")

        return self._service_factories[service_name]

    def create_qwen_service(self) -> QwenServiceInterface:
        """
        Create or get qwen service instance

        Returns:
            Configured QwenServiceInterface instance

        Raises:
            RuntimeError: If service creation fails
        """
        if self._qwen_service is None:
            try:
                qwen_factory = self.get_service_factory("qwen")
                self._qwen_service = qwen_factory.create_qwen_service()
                logger.debug("QwenService created successfully")
            except Exception as e:
                error_msg = f"Failed to create QwenService: {e}"
                logger.error(error_msg)
                raise RuntimeError(error_msg) from e

        return self._qwen_service

    def create_call_manager(self):
        """
        Create call manager instance from qwen factory

        Returns:
            Configured call manager instance
        """
        qwen_factory = self.get_service_factory("qwen")
        return qwen_factory.create_qwen_call_manager()

    def create_queue_manager(self):
        """
        Create queue manager instance from qwen factory

        Returns:
            Configured queue manager instance
        """
        qwen_factory = self.get_service_factory("qwen")
        return qwen_factory.create_queue_manager()

    def create_resource_manager(self):
        """
        Create resource manager instance from qwen factory

        Returns:
            Configured resource manager instance
        """
        qwen_factory = self.get_service_factory("qwen")
        return qwen_factory.create_resource_manager()

    def create_external_client(self):
        """
        Create external client instance from qwen factory

        Returns:
            Configured external client instance
        """
        qwen_factory = self.get_service_factory("qwen")
        return qwen_factory.create_external_client()

    def create_circuit_breaker(self):
        """
        Create circuit breaker instance from qwen factory

        Returns:
            Configured circuit breaker instance
        """
        qwen_factory = self.get_service_factory("qwen")
        return qwen_factory.create_circuit_breaker()

    async def initialize_services(self) -> None:
        """
        Initialize all created services

        Raises:
            RuntimeError: If initialization fails
        """
        try:
            logger.info("Initializing all backend services")

            # Create qwen service if not already created
            if self._qwen_service is None:
                logger.info("Creating qwen service for initialization")
                self.create_qwen_service()

            # Initialize qwen service
            if self._qwen_service and hasattr(self._qwen_service, "initialize"):
                logger.info("Initializing qwen service")
                await self._qwen_service.initialize()

            self._initialized = True
            logger.success("All backend services initialized successfully")

        except Exception as e:
            error_msg = f"Failed to initialize services: {e}"
            logger.error(error_msg)
            raise RuntimeError(error_msg) from e

    async def cleanup_all_services(self) -> None:
        """
        Cleanup all created services

        Raises:
            RuntimeError: If cleanup fails
        """
        try:
            logger.info("Cleaning up all backend services")

            if self._qwen_service:
                # Cleanup qwen service if it exists
                if hasattr(self._qwen_service, "cleanup"):
                    await self._qwen_service.cleanup()
                self._qwen_service = None

            # Clear service factories
            self._service_factories.clear()

            self._initialized = False
            logger.success("All backend services cleaned up successfully")

        except Exception as e:
            logger.error(f"Error during service cleanup: {e}")
            raise

    def is_initialized(self) -> bool:
        """
        Check if factory services are initialized

        Returns:
            True if initialized, False otherwise
        """
        return self._initialized
