"""
Qwen external service client implementation

HTTP client for qwen-service with connection pooling, retry logic,
timeout management, and comprehensive error handling.
"""

import asyncio
import random
from typing import Any, Optional

import httpx
from loguru import logger

from app.core.settings import Settings
from app.interfaces.external_service_client_interface import (
    ExternalServiceClientInterface,
)


class QwenExternalClient(ExternalServiceClientInterface):
    """
    Implementation of HTTP client for qwen-service

    Provides robust HTTP communication with qwen-service including
    connection pooling, retry logic, timeout management, and
    comprehensive error handling and monitoring.
    """

    def __init__(self, settings: Settings):
        """
        Initialize qwen external client with configuration

        Args:
            settings: Application settings containing HTTP client configuration
        """
        self.settings = settings
        self.base_url = settings.QWEN_SERVICE_API_URL
        self.timeout = settings.QWEN_SERVICE_TIMEOUT

        # HTTP client configuration
        self.max_connections = settings.QWEN_SERVICE_MAX_CONNECTIONS
        self.max_keepalive = settings.QWEN_SERVICE_MAX_KEEPALIVE
        self.keepalive_expiry = settings.QWEN_SERVICE_KEEPALIVE_EXPIRY

        # Retry configuration
        self.max_retries = settings.QWEN_MAX_RETRIES
        self.backoff_factor = settings.QWEN_RETRY_BACKOFF_FACTOR
        self.base_delay = settings.QWEN_RETRY_BASE_DELAY

        # HTTP client instance (will be initialized in initialize())
        self._client: Optional[httpx.AsyncClient] = None
        self._initialized = False

        # Statistics
        self._request_count = 0
        self._success_count = 0
        self._failure_count = 0
        self._retry_count = 0

        logger.debug(f"QwenExternalClient initialized (base_url: {self.base_url})")

    async def post(
        self,
        endpoint: str,
        data: dict[str, Any],
        timeout: Optional[float] = None,
        headers: Optional[dict[str, str]] = None,
    ) -> dict[str, Any]:
        """
        Make POST request to qwen-service

        Args:
            endpoint: API endpoint path
            data: Request payload
            timeout: Request timeout override
            headers: Additional headers

        Returns:
            Response data as dictionary

        Raises:
            httpx.HTTPError: If HTTP request fails
            RuntimeError: If service is unavailable
        """
        if not self._initialized or self._client is None:
            error_msg = "QwenExternalClient not initialized"
            raise RuntimeError(error_msg)

        url = f"{self.base_url}{endpoint}"
        request_timeout = timeout or self.timeout

        # Prepare headers
        request_headers = {
            "Content-Type": "application/json",
            "User-Agent": "backend-qwen-client/1.0",
        }
        if headers:
            request_headers.update(headers)

        self._request_count += 1

        # Retry logic with exponential backoff
        last_exception = None

        for attempt in range(self.max_retries + 1):
            try:
                logger.debug(
                    f"Making POST request to {url} (attempt {attempt + 1}/{self.max_retries + 1})"
                )

                response = await self._client.post(
                    url,
                    json=data,
                    headers=request_headers,
                    timeout=request_timeout,
                )

                response.raise_for_status()
                result = response.json()

                self._success_count += 1
                if attempt > 0:
                    self._retry_count += attempt

                logger.debug(f"POST request successful: {url}")
                return result

            except httpx.TimeoutException as e:
                last_exception = e
                logger.warning(f"Request timeout on attempt {attempt + 1}: {url}")

            except httpx.HTTPStatusError as e:
                last_exception = e
                if e.response.status_code >= 500:
                    # Server error - retry
                    logger.warning(
                        f"Server error {e.response.status_code} on attempt {attempt + 1}: {url}"
                    )
                else:
                    # Client error - don't retry
                    logger.error(f"Client error {e.response.status_code}: {url}")
                    self._failure_count += 1
                    raise RuntimeError(
                        f"HTTP {e.response.status_code}: {e.response.text}"
                    ) from e

            except httpx.RequestError as e:
                last_exception = e
                logger.warning(f"Request error on attempt {attempt + 1}: {e}")

            # Calculate delay for next attempt
            if attempt < self.max_retries:
                delay = self.base_delay * (self.backoff_factor**attempt)
                # Add jitter to prevent thundering herd
                jitter = random.uniform(0.1, 0.3) * delay
                total_delay = delay + jitter

                logger.debug(f"Retrying in {total_delay:.2f}s...")
                await asyncio.sleep(total_delay)

        # All retries exhausted
        self._failure_count += 1
        self._retry_count += self.max_retries

        error_msg = (
            f"Request failed after {self.max_retries + 1} attempts: {last_exception}"
        )
        logger.error(error_msg)
        raise RuntimeError(error_msg) from last_exception

    async def get(
        self,
        endpoint: str,
        params: Optional[dict[str, Any]] = None,
        timeout: Optional[float] = None,
        headers: Optional[dict[str, str]] = None,
    ) -> dict[str, Any]:
        """
        Make GET request to qwen-service

        Args:
            endpoint: API endpoint path
            params: Query parameters
            timeout: Request timeout override
            headers: Additional headers

        Returns:
            Response data as dictionary

        Raises:
            httpx.HTTPError: If HTTP request fails
            RuntimeError: If service is unavailable
        """
        if not self._initialized or self._client is None:
            error_msg = "QwenExternalClient not initialized"
            raise RuntimeError(error_msg)

        url = f"{self.base_url}{endpoint}"
        request_timeout = timeout or self.timeout

        # Prepare headers
        request_headers = {
            "User-Agent": "backend-qwen-client/1.0",
        }
        if headers:
            request_headers.update(headers)

        try:
            logger.debug(f"Making GET request to {url}")

            response = await self._client.get(
                url,
                params=params,
                headers=request_headers,
                timeout=request_timeout,
            )

            response.raise_for_status()
            result = response.json()

            logger.debug(f"GET request successful: {url}")
            return result

        except Exception as e:
            error_msg = f"GET request failed: {e}"
            logger.error(error_msg)
            raise RuntimeError(error_msg) from e

    async def health_check(self) -> bool:
        """
        Check if qwen-service is healthy

        Returns:
            True if service is healthy, False otherwise
        """
        try:
            # Make a simple health check request to correct qwen-service endpoint
            result = await self.get("/api/v1/health", timeout=5.0)

            # Check if response indicates healthy service
            is_healthy = result.get("status") == "healthy" or result.get(
                "healthy", False
            )

            logger.debug(f"Health check result: {is_healthy}")
            return is_healthy

        except Exception as e:
            logger.warning(f"Health check failed: {e}")
            return False

    async def get_client_stats(self) -> dict[str, Any]:
        """
        Get HTTP client statistics

        Returns:
            Dictionary containing connection pool stats and metrics
        """
        stats = {
            "request_count": self._request_count,
            "success_count": self._success_count,
            "failure_count": self._failure_count,
            "retry_count": self._retry_count,
            "success_rate": (
                (self._success_count / self._request_count * 100.0)
                if self._request_count > 0
                else 0.0
            ),
            "initialized": self._initialized,
        }

        # Add connection pool stats if client is available
        if self._client:
            try:
                # httpx doesn't expose connection pool stats directly
                # but we can add basic client info
                stats.update(
                    {
                        "base_url": str(self._client.base_url),
                        "timeout": self._client.timeout.read
                        if self._client.timeout
                        else None,
                    }
                )
            except Exception as e:
                logger.debug(f"Error getting client stats: {e}")

        return stats

    async def initialize(self) -> None:
        """
        Initialize HTTP client with connection pooling
        """
        if self._initialized:
            return

        try:
            logger.info("Initializing QwenExternalClient...")

            # Create HTTP client with connection pooling
            limits = httpx.Limits(
                max_connections=self.max_connections,
                max_keepalive_connections=self.max_keepalive,
                keepalive_expiry=self.keepalive_expiry,
            )

            timeout = httpx.Timeout(
                connect=10.0,
                read=self.timeout,
                write=10.0,
                pool=5.0,
            )

            self._client = httpx.AsyncClient(
                limits=limits,
                timeout=timeout,
                follow_redirects=True,
            )

            self._initialized = True
            logger.success("QwenExternalClient initialized successfully")

        except Exception as e:
            error_msg = f"Failed to initialize QwenExternalClient: {e}"
            logger.error(error_msg)
            raise RuntimeError(error_msg) from e

    async def close(self) -> None:
        """
        Close HTTP client and cleanup connections
        """
        if self._client:
            try:
                logger.info("Closing QwenExternalClient...")
                await self._client.aclose()
                self._client = None
                self._initialized = False
                logger.debug("QwenExternalClient closed successfully")
            except Exception as e:
                logger.error(f"Error closing QwenExternalClient: {e}")
                raise
