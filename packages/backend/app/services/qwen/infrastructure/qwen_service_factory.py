"""
Qwen service factory

Factory for creating Qwen-specific service instances using the generic
components while providing Qwen-specific configuration and customization.
"""

from loguru import logger

from app.core.settings import Settings
from app.services.qwen.infrastructure.qwen_external_client import QwenExternalClient
from app.services.qwen.interfaces import (
    GenerationRequest,
    QwenServiceInterface,
)
from app.services.qwen.services.qwen_call_manager_impl import QwenCallManagerImpl
from app.services.qwen.services.qwen_service_impl import QwenServiceImpl
from app.shared.infrastructure import CircuitBreakerImpl
from app.shared.models.service_config import ServiceConfig
from app.shared.services.generic_queue_manager import GenericQueueManager
from app.shared.services.generic_resource_manager import GenericResourceManager


class QwenServiceFactory:
    """
    Factory for creating Qwen service instances

    Creates and configures all Qwen-specific components using the generic
    infrastructure while providing Qwen-specific optimizations and settings.
    """

    def __init__(self, settings: Settings):
        """
        Initialize Qwen service factory

        Args:
            settings: Application settings containing Qwen configuration
        """
        self.settings = settings
        logger.debug("QwenServiceFactory initialized")

    def create_qwen_service(self) -> QwenServiceInterface:
        """
        Create complete Qwen service with all dependencies

        Returns:
            Configured QwenServiceInterface implementation
        """
        try:
            logger.info("Creating Qwen service with all dependencies")

            # Create Qwen-specific configuration
            config = ServiceConfig(
                name="qwen",
                max_concurrent_requests=self.settings.QWEN_MAX_CONCURRENT_REQUESTS,
                high_priority_allocation=0.6,
                medium_priority_allocation=0.3,
                low_priority_allocation=0.1,
                max_queue_size=self.settings.QWEN_MAX_QUEUE_SIZE,
                timeout=30.0,
                circuit_breaker_failure_threshold=5,
                circuit_breaker_recovery_timeout=60.0,
            )

            # Create generic components with Qwen-specific configuration
            queue_manager = GenericQueueManager[GenerationRequest](config)

            circuit_breaker = CircuitBreakerImpl(self.settings)

            resource_manager = GenericResourceManager(config, circuit_breaker)

            external_client = QwenExternalClient(self.settings)

            # Create Qwen call manager
            call_manager = QwenCallManagerImpl(
                settings=self.settings,
                queue_manager=queue_manager,
                resource_manager=resource_manager,
                external_client=external_client,
            )

            # Create Qwen service
            qwen_service = QwenServiceImpl(
                settings=self.settings,
                call_manager=call_manager,
            )

            logger.success("Qwen service created successfully")
            return qwen_service

        except Exception as e:
            error_msg = f"Failed to create Qwen service: {e}"
            logger.error(error_msg)
            raise RuntimeError(error_msg) from e

    def create_qwen_call_manager(self) -> QwenCallManagerImpl:
        """
        Create Qwen call manager with dependencies

        Returns:
            Configured QwenCallManager instance
        """
        try:
            logger.info("Creating Qwen call manager")

            # Create configuration
            config = ServiceConfig(
                name="qwen",
                max_concurrent_requests=self.settings.QWEN_MAX_CONCURRENT_REQUESTS,
                max_queue_size=self.settings.QWEN_MAX_QUEUE_SIZE,
            )

            # Create dependencies
            queue_manager = GenericQueueManager[GenerationRequest](config)
            circuit_breaker = CircuitBreakerImpl(self.settings)
            resource_manager = GenericResourceManager(config, circuit_breaker)
            external_client = QwenExternalClient(self.settings)

            # Create call manager
            call_manager = QwenCallManager(
                config=config,
                queue_manager=queue_manager,
                resource_manager=resource_manager,
                external_client=external_client,
            )

            logger.success("Qwen call manager created successfully")
            return call_manager

        except Exception as e:
            error_msg = f"Failed to create Qwen call manager: {e}"
            logger.error(error_msg)
            raise RuntimeError(error_msg) from e
