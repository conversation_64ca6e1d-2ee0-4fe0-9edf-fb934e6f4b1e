#!/usr/bin/env python3
"""
Test backend qwen external client initialization
"""

import asyncio
import sys
import os

# Add the app directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.core.settings import get_settings
from app.services.qwen.infrastructure.qwen_external_client import QwenExternalClient


async def test_qwen_external_client():
    """Test qwen external client initialization and health check"""
    print("=== Testing Qwen External Client ===\n")
    
    # Get settings
    settings = get_settings()
    print(f"Qwen service URL: {settings.QWEN_SERVICE_API_URL}")
    print(f"Timeout: {settings.QWEN_SERVICE_TIMEOUT}")
    
    # Create client
    client = QwenExternalClient(settings)
    
    try:
        # Initialize client
        print("\n1. Initializing client...")
        await client.initialize()
        print("✅ Client initialized successfully")
        
        # Test health check
        print("\n2. Testing health check...")
        is_healthy = await client.health_check()
        print(f"Health check result: {is_healthy}")
        
        if is_healthy:
            print("✅ Qwen service is healthy")
        else:
            print("❌ Qwen service is not healthy")
        
        # Test direct API call
        print("\n3. Testing direct API call...")
        try:
            result = await client.post(
                endpoint="/api/v1/generate",
                data={
                    "prompt": "مرحبا",
                    "system_prompt": "أنت مساعد ذكي",
                    "enable_thinking": False
                },
                timeout=10.0
            )
            print("✅ Direct API call successful")
            print(f"Generated text: {result.get('generated_text', 'N/A')}")
        except Exception as e:
            print(f"❌ Direct API call failed: {e}")
        
    except Exception as e:
        print(f"❌ Client initialization failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Cleanup
        try:
            await client.close()
            print("\n✅ Client closed successfully")
        except Exception as e:
            print(f"❌ Error closing client: {e}")


async def test_direct_qwen_connection():
    """Test direct connection to qwen-service"""
    print("\n=== Testing Direct Qwen Connection ===\n")
    
    import httpx
    
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            # Test health endpoint
            print("Testing qwen-service health endpoint...")
            response = await client.get("http://localhost:8004/api/v1/health")
            print(f"Health endpoint status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"Health response: {result}")
            else:
                print(f"Health endpoint failed: {response.text}")
            
            # Test generate endpoint
            print("\nTesting qwen-service generate endpoint...")
            response = await client.post(
                "http://localhost:8004/api/v1/generate",
                json={
                    "prompt": "مرحبا",
                    "system_prompt": "أنت مساعد ذكي",
                    "enable_thinking": False
                }
            )
            print(f"Generate endpoint status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"Generated text: {result.get('generated_text', 'N/A')}")
            else:
                print(f"Generate endpoint failed: {response.text}")
                
    except Exception as e:
        print(f"❌ Direct connection test failed: {e}")


async def main():
    """Main test function"""
    await test_direct_qwen_connection()
    print("\n" + "="*60)
    await test_qwen_external_client()


if __name__ == "__main__":
    asyncio.run(main())
